# Flag-40数据集问题构造项目文档

## 任务概述

基于**flag-40.csv**数据集构造出三道题目，包含以下三个类型：

- 不带模板的归因问题
- 带模板的归因问题
- 带模板的开放问题

**核心要求**：

- 其中一部分题目需要领域知识才能解题
- 基于构造的问题，考虑数据集的扩展方案（只增加行，不改列）

## 数据集分析

**flag-40.csv**数据集：某大型零售企业的销售交易系统，记录2024年全年各门店、各品类的销售交易及处理情况。

### 数据结构分析

| 字段名   | 数据类型 | 业务含义               | 取值范围                                                |
| -------- | -------- | ---------------------- | ------------------------------------------------------- |
| 编号     | UUID     | 交易单唯一标识         | 系统生成                                                |
| 创建时间 | DateTime | 交易发生时间           | 2024年全年                                              |
| 金额     | Decimal  | 交易金额（元）         | 1-50,000                                                |
| 状态     | Enum     | 交易处理状态           | Completed/Cancelled/Pending/Processing                  |
| 简短描述 | Text     | 商品描述               | 包含关键词：Electronics/Clothing/Food/Books/Sports      |
| 配置项   | Enum     | 商品配置类型           | premium/standard/basic/economy等                        |
| 用户     | String   | 客户ID                 | 客户账号                                                |
| 部门     | Enum     | 销售门店               | Store_A/Store_B/Store_C/Store_D/Store_E/Store_F/Store_G |
| 类别     | Enum     | 商品类别               | Electronics/Clothing/Food/Books                         |
| 处理日期 | DateTime | 交易完成时间           | 2024年全年                                              |
| 来源ID   | UUID     | 关联的促销活动         | 系统生成                                                |
| 类型     | Enum     | 交易类型               | Online/Offline                                          |
| 地点     | Enum     | 销售区域               | North/South/East/West/Central                           |
| 处理周期 | Integer  | 处理天数（发货到收货） | 1-30天                                                  |

### 需要增加的专业字段

| 字段名       | 数据类型 | 业务含义         | 示例值                 |
| ------------ | -------- | ---------------- | ---------------------- |
| 客户等级     | Enum     | 客户VIP等级      | 普通, 银卡, 金卡, 钻石 |
| 是否首次购买 | Boolean  | 是否为首次购买   | True, False            |
| 标准零售价   | Decimal  | 商品标准零售价格 | 100.00, 299.99         |
| 实收金额     | Decimal  | 实际收取金额     | 80.00, 249.99          |
| 促销活动ID   | String   | 关联的促销活动   | PROMO001, PROMO002     |
| 促销费用     | Decimal  | 促销活动费用     | 5000.00, 10000.00      |
| 促销目标     | Decimal  | 促销目标销售金额 | 50000.00, 100000.00    |
| 商品数量     | Integer  | 购买商品数量     | 1, 2, 5, 10            |
| 订单号       | String   | 关联的订单号     | ORD001, ORD002         |
| 会员积分     | Integer  | 获得的会员积分   | 10, 50, 100            |

## 领域知识体系

### 核心指标计算公式

#### 用户维度指标（增加用户字段）：

- **客单价** = 销售总金额 / 有交易的顾客总数
- **件单价** = 销售总金额 / 销售总数量
- **连带率** = 销售总数量 / 成交订单总数

#### 商品维度指标（增加折扣信息）：

- **折扣率** = 商品实收金额 / 商品标准零售价金额
- **品类结构占比** = 某品类销售额 / 总销售额

#### 促销维度指标（增加促销字段）：

- **费销比** = 促销费用金额 / 促销期间的销售额
- **目标完成率** = 促销期间销售金额 / 促销目标销售金额

## 构造题目

### 题目一：不带模板的归因问题

**题目背景**：
你是该科技企业基于ServiceNow平台的费用管理系统分析师。根据最新的运营数据发现，虽然Development部门在处理效率方面表现最佳（平均处理周期最短），但其费用申请的最终通过率却明显低于其他部门，这一矛盾现象引起了管理层的关注。

**问题描述**：
请基于flag-40.csv数据，运用ServiceNow费用管理的四大专业模块知识，深入分析Development部门"处理快但通过率低"这一矛盾现象的根本原因，并提出针对性的改进建议。

**分析要求**：

1. **审批流程管理分析**：计算Development部门的状态转换成功率、处理周期标准差，识别流程异常
2. **费用控制管理分析**：计算该部门的金额集中度、关键词权重影响，分析费用特征
3. **合规风险管理分析**：计算异常申请比例、配置项风险分布，识别合规问题
4. **业务场景管理分析**：如涉及外部服务，分析服务质量对审批结果的影响
5. 基于四个模块的综合分析，提出系统化改进方案

**领域知识要求**：

- 掌握**费用偏差率**的计算方法和业务含义
- 理解**供应商综合评分**的权重配置和评分逻辑

**评估标准**：

- 能否正确应用四个业务模块的专业计算公式
- 是否运用了审批流程、费用控制、合规风险的综合分析框架
- 能否基于配置项分类和关键词权重进行深度分析
- 改进建议是否体现了ServiceNow平台的专业管理理念

### 题目二：带模板的归因问题

**题目背景**：
作为企业ServiceNow平台的运营分析师，你发现2024年第四季度（10-12月）费用申请处理效率出现显著下降。根据**关键词权重影响**分析，Travel关键词（权重系数1.5）相关费用的处理时间大幅增加，这与年底业务冲刺期的预期相矛盾。需要运用四大专业模块的知识进行系统性分析。

**分析要求**：

1. 计算第四季度与前三季度的平均处理周期对比
2. 按关键词分析处理时间变化：Travel、Cloud、Service、Equipment、Asset
3. 按金额区间分析效率变化：<$100、$100-$500、$500-$5000、>$5000
4. 按部门分析效率对比：Development、Finance等部门的处理时间变化
5. 提出针对性的优化建议

**领域知识要求**：

- 理解Travel、Cloud、Service等关键词对费用金额和处理时间的影响规律

### 题目三：带模板的开放问题（周报）

# 任务目标

你是某科技企业ServiceNow费用管理系统的运营分析师，需要面向财务总监输出周度费用申请分析报告：帮助管理层了解过去7天（20241021-20241027）的费用申请变化情况，以反映业务进展或提示管理风险

# 分析内容

## 当周概况

### 当周数据概况

按照[费用类别]、[申请类型]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
使用折线图，展现最近14天，各个 `费用类别`、`申请类型`每天的 `单日申请金额`趋势图，并解读数据
按照[一级业务场景]、[二级业务场景]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
使用折线图，展现最近14天，各[一级业务场景]、[二级业务场景]每天的 `单日申请金额`趋势图，并解读数据

### 当周数据波动归因

使用表格，审批团队维度呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，说明当周环比变化在审批团队维度上的主要原因
围绕每个环比上周增长率的绝对值≥15%的审批团队维度
下钻到部门分析增长值，定位主要影响部门
下钻到部门+类别维度分析增长值，定位主要影响的部门+类别维度
下钻到部门+配置项维度分析增长值，定位主要影响的部门+配置项维度
基于上述的多轮下钻，明确主要影响变化的部门+类别+配置项，形成表格，并解读数据

# 字段使用

## 核心指标

`日均申请金额`
`单日申请金额`

## 核心维度

### 审批团队维度

[费用审批主管团队]：对于全量权限用户，按此拆解申请金额
[费用审批小组]：对于审批Leader，按此拆解申请金额。审批Leader访问时，满足[审批负责人Leader姓名] = [用户姓名]
[费用审批二级小组]：对于审批组长，按此拆解申请金额。审批组长访问时，满足[审批负责人组长姓名] = [用户姓名]
[费用审批负责人姓名]：对于审批组长、审批二级组长，按此拆解申请金额。审批组长访问时，满足[审批负责人组长姓名] = [用户姓名]；审批二级组长访问时，满足[审批负责人二级组长姓名] = [用户姓名]
审批员访问时，满足[费用审批负责人姓名] = [用户姓名]，无需在审批团队维度进行拆解

### 部门维度

[主部门名称]：表格展示中，使用该字段
[主部门简称]：文字描述中，使用该字段
[部门负责人审批团队]：表格展示中，随[主部门名称]呈现
[部门负责人审批小组]：表格展示中，随[主部门名称]呈现
[部门负责人姓名]：表格展示中，随[主部门名称]呈现

### 费用类别维度

[费用类别]：费用维度的第一层拆解，体现申请金额在不同费用类别的分布，也是业务上的基本分布
[申请类型]：费用维度的第二层拆解，体现申请金额在不同申请类型的分布，比如区分一次性和周期性申请
[配置项类型]：费用维度的第三层拆解，体现申请金额在不同配置项的分布，比如heart、truth、certain
[具体配置项]：费用维度的第四层拆解，体现申请金额在具体配置项值的分布

### 业务场景维度

[一级业务场景]：场景维度的第一层拆解，体现申请金额在业务场景上的基本分布
[二级业务场景]：场景维度的第二层拆解，体现申请金额在具体业务场景上的基本分布

### 时间维度

使用[创建时间]作为时间维度
当月：date_format([创建时间], 'yyyy-MM') = date_format([p_date], 'yyyy-MM')，表示当前这个月份
上月：date_format([创建时间], 'yyyy-MM') = date_format(month_add([p_date], -1), 'yyyy-MM')，表示上月
当周：[创建时间] between date_add([p_date], -6) AND [p_date]，表示当前这一周
上周：[创建时间] between date_add([p_date], -13) AND date_add([p_date], -7)，表示上周

### 其他分析维度

审批效率分布
[一级审批效率分布]
[二级审批效率分布]

**领域知识要求**：

- 熟悉多维度下钻分析方法：部门→部门+类别→部门+配置项
