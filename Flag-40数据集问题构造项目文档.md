# Flag-40数据集问题构造项目文档

## 任务概述

基于**flag-40.csv**数据集构造出三道题目，包含以下三个类型：

- 不带模板的归因问题
- 带模板的归因问题
- 带模板的开放问题

**核心要求**：

- 其中一部分题目需要领域知识才能解题
- 基于构造的问题，考虑数据集的扩展方案（只增加行，不改列）

## 数据集分析

**flag-40.csv**数据集：某科技企业基于ServiceNow平台的费用管理系统，记录2024年全年各部门的费用申请及处理情况。

### 数据结构分析

| 字段名   | 数据类型 | 业务含义               | 取值范围                                                            |
| -------- | -------- | ---------------------- | ------------------------------------------------------------------- |
| 编号     | UUID     | 费用申请单唯一标识     | 系统生成                                                            |
| 创建时间 | DateTime | 费用申请创建时间       | 2024年全年                                                          |
| 金额     | Decimal  | 申请金额（元）         | 1-100,000                                                           |
| 状态     | Enum     | 申请处理状态           | Processed/Declined/Pending/Submitted                                |
| 简短描述 | Text     | 费用申请描述           | 包含关键词：Equipment/Service/Cloud/Asset等                         |
| 配置项   | Enum     | ServiceNow配置项类型   | heart/truth/certain/among等                                         |
| 用户     | String   | 申请人ID               | 员工账号                                                            |
| 部门     | Enum     | 申请部门               | HR/Finance/Development/Customer Support/IT/Sales/Product Management |
| 类别     | Enum     | 费用类别               | Assets/Services/Miscellaneous                                       |
| 处理日期 | DateTime | 申请处理完成时间       | 2024年全年                                                          |
| 来源ID   | UUID     | 关联的业务来源         | 系统生成                                                            |
| 类型     | Enum     | 申请类型               | One-time/Recurring                                                  |
| 地点     | Enum     | 业务区域               | North America/Europe/Asia/Africa                                    |
| 处理周期 | Integer  | 处理天数（申请到完成） | -300到300天（负数表示提前处理）                                     |

### 需要增加的专业字段

| 字段名     | 数据类型 | 业务含义           | 示例值                              |
| ---------- | -------- | ------------------ | ----------------------------------- |
| 审批层级   | Enum     | ServiceNow审批层级 | L1/L2/L3                            |
| 会签部门   | Enum     | 需要会签的部门     | 资产管理部/信息安全部/采购部/无     |
| 预算科目   | Enum     | 预算科目分类       | 运营费用/资本支出/研发费用/营销费用 |
| 供应商类型 | Enum     | 供应商分类         | 战略伙伴/一般供应商/新供应商/内部   |
| 风险等级   | Enum     | 费用申请风险等级   | 高风险/中风险/低风险                |
| 预算状态   | Enum     | 预算执行状态       | 充足/紧张/超支预警/已超支           |
| 合规等级   | Enum     | 合规要求等级       | A级(严格)/B级(标准)/C级(简化)       |

## 专业领域知识体系

### 核心专业知识（精选3个）

#### 专业知识1：ServiceNow配置项风险分级机制

**业务规则**：

- **heart配置项**：涉及核心业务系统，需要变更管理委员会审批，风险评估严格，通常处理周期15-30天
- **truth配置项**：涉及数据处理和安全，需要数据安全评估，合规要求高，处理周期10-20天
- **certain配置项**：标准化配置，技术风险低，可走快速审批通道，处理周期3-7天
- **among配置项**：共享资源配置，需要多部门协调确认，协调复杂度高，处理周期不定

**业务影响**：不同配置项类型直接影响审批流程复杂度和处理时间，是费用管理的核心分类依据。

#### 专业知识2：部门预算管控差异化机制

**业务规则**：

- **Development部门**：采用敏捷预算模式，5万以下可部门内快速审批，但总预算控制严格
- **Finance部门**：传统财务管控模式，所有费用需要CFO最终审批，层层把关确保合规
- **IT部门**：技术导向审批，重点关注技术可行性和安全性，对技术类费用审批相对宽松
- **Sales部门**：业绩导向审批，费用审批与销售目标完成情况关联度高

**业务影响**：不同部门的预算管控机制差异导致审批效率和通过率存在显著差异。

#### 专业知识3：ServiceNow关键词权重影响机制

**业务规则**：

- **Equipment关键词**：固定资产类费用，需要资产管理部门会签，涉及折旧和资产登记
- **Service关键词**：外部服务采购，需要供应商资质验证和合同审批流程
- **Cloud关键词**：云服务类费用，需要信息安全部门评估，关注数据安全和合规性
- **Asset关键词**：资产管理相关，需要资产价值评估和盘点确认

**业务影响**：关键词直接决定审批流程的复杂程度和需要涉及的会签部门数量。

## 构造题目

### 题目一：不带模板的归因问题

**题目背景**：
你是某科技企业ServiceNow费用管理系统的运营分析师。通过数据分析发现，Development部门虽然平均处理周期最短（46天），但费用申请的最终通过率却明显低于Finance部门（63.6天）和HR部门（60.6天）。这一"处理快但通过率低"的矛盾现象引起了CFO的关注。

**归因问题**：
请基于flag-40.csv数据，运用ServiceNow费用管理的专业知识，深入分析Development部门出现"处理快但通过率低"这一矛盾现象的根本原因是什么？

**分析要求**：

1. 从配置项风险分级角度分析Development部门申请特征
2. 从部门预算管控机制角度分析审批模式差异
3. 从关键词权重影响角度分析费用申请内容特点
4. 综合分析并提出根本原因和改进建议

**需要的专业知识**：

- ServiceNow配置项风险分级机制的业务规则
- 不同部门预算管控差异化机制的运作原理
- 关键词对审批流程影响的业务逻辑

**评估标准**：

- 能否正确运用三个专业知识体系进行分析
- 是否准确识别了矛盾现象的根本原因
- 改进建议是否具有针对性和可操作性

**数据集改造设计**：

**步骤一：构造通过率下降现象**
构造2024年9-10月期间Development部门大量Cloud相关申请，但状态为 `Declined`的数据：

```csv
# 示例数据：Development部门9-10月Cloud申请大量被拒绝
dev_001,2024-09-05,25000,Declined,AWS cloud migration services,truth,dev_user1,Development,Services,2024-09-18,xxx,One-time,Asia,13
dev_002,2024-09-08,18000,Declined,Cloud database setup,truth,dev_user2,Development,Services,2024-09-20,xxx,One-time,Asia,12
dev_003,2024-09-12,32000,Declined,Cloud security assessment,truth,dev_user3,Development,Services,2024-09-25,xxx,One-time,Asia,13
dev_004,2024-10-03,28000,Declined,Cloud storage migration,truth,dev_user4,Development,Services,2024-10-16,xxx,One-time,Asia,13
dev_005,2024-10-08,22000,Declined,Cloud monitoring tools,truth,dev_user5,Development,Services,2024-10-21,xxx,One-time,Asia,13
```

**目的**：体现Development部门费用申请的最终通过率远低于其他部门

**步骤二：构造处理周期短现象**
设置较短的处理周期数据（5-15天），明显短于其他部门的20-35天：

- Development部门9-10月：处理周期集中在5-15天
- 其他部门同期：处理周期保持20-35天
- Development部门其他月份：处理周期正常20-30天

**目的**：体现Development部门在2024年9-10月期间平均处理周期最短

**步骤三：增加原因分析字段**
新增 `风险等级`字段，表明这段时间增加的Cloud相关申请风险等级过高导致Declined：

```csv
# 新增字段示例
风险等级: 高风险  # Cloud迁移项目涉及数据安全，风险等级高
供应商类型: 新供应商  # AWS是新引入的云服务供应商
会签部门: 信息安全部  # 需要额外的安全审批
```

**目的**：提供专业知识分析的数据基础，解释为什么处理快但通过率低

**改造价值**：

- 直接支撑"处理快但通过率低"的矛盾现象分析
- 提供基于专业知识的原因分析数据基础
- 体现真实的业务场景（云服务迁移项目）

### 题目二：带模板的归因问题

**题目背景**：
作为ServiceNow费用管理系统分析师，你发现2024年第四季度（10-12月）整体费用申请通过率相比前三季度下降了15%，特别是包含"Cloud"关键词的申请通过率下降最为明显。需要按照标准分析框架找出原因。

**归因问题**：
请按照以下分析模板，系统分析第四季度费用申请通过率下降的根本原因。

**分析模板**：

```
## 第四季度费用申请通过率下降归因分析

### 1. 配置项维度分析
- 计算各配置项类型（heart/truth/certain/among）的通过率变化
- 分析配置项风险分级对通过率的影响

### 2. 部门维度分析
- 计算各部门第四季度vs前三季度通过率对比
- 基于部门预算管控机制分析差异原因

### 3. 关键词维度分析
- 重点分析Cloud关键词相关申请的通过率变化
- 结合关键词权重影响机制分析原因

### 4. 综合归因结论
- 基于三个维度分析，确定通过率下降的主要原因
- 提出针对性改进措施
```

**需要的专业知识**：

- ServiceNow配置项风险分级机制
- 部门预算管控差异化机制
- 关键词权重影响机制

**数据集改造设计**：

**步骤一：构造Q4通过率下降现象**
构造2024年Q4期间Cloud关键词相关申请大量被拒绝的数据：

```csv
# Q4 Cloud相关申请大量Declined
cloud_001,2024-10-05,35000,Declined,Cloud infrastructure upgrade,truth,user1,Development,Services,2024-10-20,xxx,One-time,Asia,15
cloud_002,2024-11-08,28000,Declined,Cloud security services,truth,user2,IT,Services,2024-11-25,xxx,One-time,Europe,17
cloud_003,2024-12-12,42000,Declined,Cloud data migration,truth,user3,Development,Services,2024-12-28,xxx,One-time,Asia,16
```

**目的**：体现Q4期间Cloud关键词申请通过率显著下降（从80%降到40%）

**步骤二：构造对比基线数据**
构造Q1-Q3期间相同类型申请的正常通过率数据：

```csv
# Q1-Q3 Cloud相关申请正常通过率
cloud_base1,2024-03-05,33000,Processed,Cloud storage services,truth,user1,Development,Services,2024-03-18,xxx,One-time,Asia,13
cloud_base2,2024-06-08,29000,Processed,Cloud backup solution,truth,user2,IT,Services,2024-06-22,xxx,One-time,Europe,14
```

**目的**：提供对比基线，体现Q4的异常下降

**步骤三：增加原因分析字段**
新增字段解释通过率下降的原因：

```csv
# 新增字段示例
配置项风险: 高风险  # truth配置项在Q4风险评估更严格
关键词权重: 1.8     # Cloud关键词权重高，审批更严格
会签部门数: 3       # Q4增加了额外的会签要求
```

**目的**：提供专业知识分析的数据基础，解释Q4通过率下降的根本原因

**改造价值**：

- 直接支撑Q4 vs Q1-Q3的通过率对比分析
- 提供Cloud关键词影响的量化分析基础
- 体现配置项风险分级机制的业务影响

### 题目三：带模板的开放问题（周报）

**任务目标**：
你是某科技企业ServiceNow费用管理系统的运营分析师，需要面向CFO输出周度费用管理分析报告，帮助管理层了解过去7天（2024年10月21日-10月27日）的费用申请处理情况，识别管理风险和优化机会。

**分析模板**：

```
# ServiceNow费用管理周度分析报告

## 当周概况

### 当周数据概况
按照[配置项类型]、[部门]呈现当周日均申请金额、环比上周增长率，按日均金额降序排列，使用表格展示
使用折线图展现最近14天各[配置项类型]每天的申请金额趋势图

### 当周数据波动归因
使用表格，按部门维度呈现当周日均申请金额、环比上周增长率
围绕每个环比增长率绝对值≥15%的部门：
- 下钻到部门+配置项维度分析增长值
- 下钻到部门+关键词维度分析增长值
- 下钻到部门+金额区间维度分析增长值
基于多轮下钻，明确主要影响变化的部门+配置项+关键词组合

## 专业维度分析

### 配置项风险分级分析
基于ServiceNow配置项风险分级机制，分析各风险等级申请的处理效率和通过率

### 部门预算管控分析
基于部门预算管控差异化机制，分析各部门预算执行情况和审批模式

### 关键词权重影响分析
基于关键词权重影响机制，分析不同关键词对审批结果的影响趋势

## 核心指标
- 日均申请金额
- 单日申请金额
- 申请通过率
- 平均处理周期

## 时间维度定义
- 当周：[创建时间] between date_add([p_date], -6) AND [p_date]
- 上周：[创建时间] between date_add([p_date], -13) AND date_add([p_date], -7)
```

**需要的专业知识**：

- ServiceNow配置项风险分级机制
- 部门预算管控差异化机制
- 关键词权重影响机制
- 多维度下钻分析方法：部门→部门+配置项→部门+关键词

**数据集改造设计**：

**步骤一：构造当周异常申请现象**
构造2024年10月第3周（10月21日-10月27日）Development部门异常高频的Cloud费用申请：

```csv
# 当周Development部门异常高频申请
week_001,2024-10-21,25000,Pending,Cloud server migration phase 1,truth,dev_user1,Development,Services,2024-11-05,xxx,One-time,Asia,15
week_002,2024-10-22,18000,Declined,Cloud database backup service,truth,dev_user2,Development,Services,2024-11-08,xxx,One-time,Asia,17
week_003,2024-10-23,32000,Pending,Cloud security monitoring,truth,dev_user3,Development,Services,2024-11-10,xxx,One-time,Asia,18
week_004,2024-10-24,28000,Declined,Cloud development environment,truth,dev_user4,Development,Services,2024-11-12,xxx,One-time,Asia,19
week_005,2024-10-25,22000,Pending,Cloud data analytics tools,truth,dev_user5,Development,Services,2024-11-15,xxx,One-time,Asia,21
```

**目的**：体现当周Development部门申请金额环比上周增长超过50%的异常现象

**步骤二：构造对比基线数据**
构造上周（10月14日-10月20日）和其他部门同期的正常申请模式：

```csv
# 上周Development部门正常申请量
last_week_001,2024-10-15,15000,Processed,Standard equipment purchase,certain,dev_user1,Development,Assets,2024-10-28,xxx,One-time,Asia,13
last_week_002,2024-10-18,12000,Processed,Software license renewal,certain,dev_user2,Development,Services,2024-10-30,xxx,Recurring,Asia,12

# 当周其他部门正常申请
other_dept_001,2024-10-22,20000,Processed,Office supplies purchase,certain,fin_user1,Finance,Assets,2024-11-05,xxx,One-time,Asia,14
```

**目的**：提供环比分析的基线数据，突出Development部门的异常变化

**步骤三：增加下钻分析字段**
新增支持多维度下钻分析的字段：

```csv
# 新增字段示例
配置项风险等级: 高风险    # truth配置项风险等级高
部门预算模式: 敏捷型      # Development部门采用敏捷预算模式
关键词类型: Cloud        # 主要关键词为Cloud
金额区间: 中额           # 单笔金额15-35万属于中额区间
申请周期: W43           # 2024年第43周
```

**目的**：支持部门→部门+配置项→部门+关键词的多层级下钻分析

**改造价值**：

- 直接支撑周度异常现象的识别和分析
- 提供多维度下钻分析的完整数据基础
- 体现真实的业务场景（云迁移项目集中申请期）
- 满足CFO级别报告的专业分析要求

## 综合数据集扩展方案

基于三道题目的分析需求，建议在现有flag-40.csv基础上增加以下字段（只增加行，不改列结构）：

### 核心新增字段设计

| 字段名         | 数据类型 | 业务含义           | 取值范围                      | 支持的题目     | 专业知识关联           |
| -------------- | -------- | ------------------ | ----------------------------- | -------------- | ---------------------- |
| 审批结果       | Enum     | 最终审批结果       | 通过/拒绝/待审批              | 题目一、题目二 | 支持通过率分析         |
| 配置项风险等级 | Enum     | 配置项风险分级     | 高风险/中风险/低风险          | 所有题目       | 配置项风险分级机制     |
| 部门预算模式   | Enum     | 部门预算管控模式   | 敏捷型/传统型/技术型/业绩型   | 所有题目       | 部门预算管控差异化机制 |
| 关键词类型     | Enum     | 主要关键词分类     | Equipment/Service/Cloud/Asset | 所有题目       | 关键词权重影响机制     |
| 审批层级       | Enum     | ServiceNow审批层级 | L1/L2/L3                      | 题目一、题目三 | 审批流程复杂度分析     |
| 申请季度       | String   | 申请所属季度       | Q1/Q2/Q3/Q4                   | 题目二         | 季度趋势分析           |
| 申请周期       | String   | 申请所属周期       | W1-W52                        | 题目三         | 周度分析               |
| 关键词权重     | Decimal  | 关键词影响权重     | 0.5-2.0                       | 题目二         | 关键词权重量化分析     |
| 会签部门数     | Integer  | 需要会签的部门数量 | 0-5                           | 题目二、题目三 | 跨部门协调复杂度分析   |
| 金额区间       | Enum     | 申请金额区间       | 小额/中额/大额/特大额         | 题目三         | 金额区间下钻分析       |

### 完整数据扩展示例

```csv
编号,创建时间,金额,状态,简短描述,配置项,用户,部门,类别,处理日期,来源ID,类型,地点,处理周期,审批结果,配置项风险等级,部门预算模式,关键词类型,审批层级,申请季度,申请周期,关键词权重,会签部门数,金额区间
03ce8b0b-59b6-40f3-8662-dc95761709db,2024-10-08 09:37:41,25504.2,Processed,Even dark time foreign beat better. Equipment,heart,donald65,Development,Assets,2024-10-20 07:16:48,6ca54cf2-55b1-428e-9ce5-431e73b04ac6,One-time,North America,11,拒绝,高风险,敏捷型,Equipment,L3,Q4,W41,1.5,2,中额
c256e837-391d-45b1-9f64-12f23a3b23f3,2024-10-18 20:34:43,25520.23,Declined,View trade catch realize generation rest head. Service Cloud Asset Equipment,truth,frank_larsen,Development,Services,2024-01-15 09:59:28,3708733d-2c91-4667-8da4-f6bc2baa87b5,Recurring,Europe,-278,拒绝,中风险,敏捷型,Cloud,L2,Q4,W42,1.8,2,中额
d92a3103-70df-47e9-8bd4-fa94eb0db491,2024-07-09 04:04:28,97901.7,Declined,Factor receive site end education clearly. Cloud,treat,rivera_holly,Customer Support,Assets,2024-01-26 15:47:09,f42e7b37-afe2-4d47-9e51-5dd44dabca8a,One-time,Africa,-165,拒绝,中风险,传统型,Cloud,L3,Q3,W28,1.8,3,特大额
```

### 扩展价值总结

1. **支持专业分析**：每个字段都对应特定的专业知识体系，需要领域知识才能正确解读
2. **满足题目需求**：覆盖三道题目的所有分析维度，支持复杂的归因分析和下钻分析
3. **保持数据一致性**：只增加列，不改变现有数据结构，保证向后兼容
4. **体现业务价值**：新增字段都有明确的业务含义，不是简单的计算指标
5. **支持多维分析**：提供足够的维度组合，支持部门→部门+配置项→部门+关键词的多层下钻

通过这样的设计，数据集既保持了简洁性，又具备了足够的专业深度，能够支持需要ServiceNow费用管理领域知识的复杂分析场景。
